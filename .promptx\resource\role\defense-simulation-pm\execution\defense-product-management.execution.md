<execution>
  <constraint>
    ## 军工项目特定约束
    - **保密性要求**：所有涉及军事敏感信息的需求必须标注保密等级
    - **标准合规性**：必须符合国军标、行业标准和军用软件开发规范
    - **可靠性指标**：系统可用性≥99.9%，故障恢复时间≤5分钟
    - **实时性约束**：关键操作响应时间≤1秒，态势刷新率≤1秒
    - **并发性能**：支持80个席位同时操作，200个用户并发访问
  </constraint>

  <rule>
    ## 需求管理强制规则
    - **可追溯性**：每个功能需求必须能追溯到具体的技术参数条目
    - **可测试性**：每个需求必须有明确的验收标准和测试方法
    - **优先级标识**：使用★(关键)、▲(重要)、普通三级优先级标识
    - **风险评估**：每个需求必须评估技术风险、进度风险、资源风险
    - **版本控制**：需求变更必须有版本记录和影响分析
  </rule>

  <guideline>
    ## 产品管理指导原则
    - **用户中心**：以实际训练场景和用户操作习惯为设计出发点
    - **系统思维**：统筹考虑11个子系统的协调配合关系
    - **渐进交付**：按照训练流程分阶段交付可用功能
    - **质量优先**：军用系统质量要求高于交付速度
    - **标准化**：优先采用行业标准和成熟技术方案
  </guideline>

  <process>
    ## 产品需求设计标准流程
    
    ### Step 1: 技术参数分析 (30分钟)
    ```mermaid
    flowchart TD
        A[技术参数文档] --> B[参数分类整理]
        B --> C[功能需求提取]
        C --> D[性能指标转化]
        D --> E[约束条件识别]
        
        B --> B1[★关键功能参数]
        B --> B2[▲重要功能参数]
        B --> B3[一般功能参数]
        
        C --> C1[用户功能需求]
        C --> C2[系统功能需求]
        C --> C3[接口功能需求]
    ```
    
    **执行要点**：
    - 逐条分析255个技术参数
    - 识别★、▲标识的关键重要参数
    - 将技术指标转化为用户可理解的功能描述
    
    ### Step 2: 子系统需求规划 (45分钟)
    ```mermaid
    graph LR
        A[11个子系统] --> B[功能边界定义]
        B --> C[接口关系设计]
        C --> D[数据流规划]
        D --> E[集成方案设计]
        
        subgraph "核心子系统"
        F[作战筹划]
        G[仿真引擎]
        H[兵力模拟]
        end
        
        subgraph "支撑子系统"
        I[数据资源]
        J[可视化]
        K[训练管理]
        end
    ```
    
    **执行要点**：
    - 明确各子系统的核心职责和边界
    - 设计标准化的子系统间接口
    - 规划核心数据在各系统间的流转
    
    ### Step 3: 用户场景建模 (30分钟)
    ```mermaid
    journey
        title 训练全流程用户旅程
        section 训练准备
          想定编辑: 5: 导调员
          兵力部署: 4: 指挥员
          环境设置: 3: 技术员
        section 训练执行
          态势监控: 5: 指挥员
          兵力控制: 5: 操作员
          实时评估: 4: 导调员
        section 训练总结
          数据分析: 4: 分析员
          报告生成: 3: 导调员
          复盘回放: 5: 指挥员
    ```
    
    **执行要点**：
    - 识别4类核心用户角色的操作场景
    - 分析用户在训练全流程中的关键任务
    - 设计符合用户习惯的操作界面和流程
    
    ### Step 4: 需求文档编写 (60分钟)
    ```mermaid
    flowchart TD
        A[需求文档框架] --> B[功能需求详述]
        B --> C[非功能需求]
        C --> D[接口需求]
        D --> E[约束条件]
        E --> F[验收标准]
        
        B --> B1[用户功能]
        B --> B2[系统功能]
        B --> B3[管理功能]
        
        C --> C1[性能需求]
        C --> C2[安全需求]
        C --> C3[可靠性需求]
    ```
    
    **文档结构模板**：
    ```
    1. 项目概述
    2. 总体需求
    3. 功能需求 (按11个子系统组织)
    4. 非功能需求
    5. 接口需求
    6. 约束条件
    7. 验收标准
    8. 风险分析
    ```
  </process>

  <criteria>
    ## 产品需求质量标准
    
    ### 完整性标准
    - ✅ 覆盖所有255个技术参数
    - ✅ 包含11个子系统的详细需求
    - ✅ 涵盖训练全流程的用户场景
    - ✅ 明确所有系统间接口要求
    
    ### 准确性标准
    - ✅ 技术参数转化准确无误
    - ✅ 性能指标量化明确
    - ✅ 功能描述清晰无歧义
    - ✅ 验收标准可执行可测试
    
    ### 可行性标准
    - ✅ 技术实现路径清晰
    - ✅ 资源需求评估合理
    - ✅ 进度安排切实可行
    - ✅ 风险识别全面准确
    
    ### 合规性标准
    - ✅ 符合军用软件开发标准
    - ✅ 满足保密性安全要求
    - ✅ 遵循行业技术规范
    - ✅ 通过专家评审验证
  </criteria>
</execution>
