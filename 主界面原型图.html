<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作战模拟仿真训练系统主界面原型</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #1f2937;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 顶部导航栏 */
        .header {
            background: #1e3a8a;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            border-bottom: 2px solid #3b82f6;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logo {
            width: 40px;
            height: 40px;
            background: #ef4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .system-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 30px;
            font-size: 14px;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            display: inline-block;
            margin-right: 5px;
        }
        
        /* 主体布局 */
        .main-container {
            display: flex;
            height: calc(100vh - 90px);
        }
        
        /* 左侧菜单 */
        .sidebar {
            width: 280px;
            background: #374151;
            border-right: 1px solid #4b5563;
            overflow-y: auto;
        }
        
        .menu-group {
            margin: 10px 0;
        }
        
        .menu-title {
            padding: 15px 20px;
            background: #4b5563;
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            border-left: 4px solid #3b82f6;
        }
        
        .menu-items {
            background: #374151;
        }
        
        .menu-item {
            padding: 12px 40px;
            cursor: pointer;
            font-size: 13px;
            border-bottom: 1px solid #4b5563;
            transition: background 0.2s;
        }
        
        .menu-item:hover {
            background: #4b5563;
        }
        
        .menu-item.active {
            background: #3b82f6;
            color: #ffffff;
        }
        
        /* 中央态势区 */
        .main-content {
            flex: 1;
            background: #000000;
            position: relative;
            border: 1px solid #4b5563;
        }
        
        .battlefield-view {
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #001122 0%, #003366 50%, #001122 100%);
            position: relative;
            overflow: hidden;
        }
        
        .battlefield-overlay {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            display: flex;
            justify-content: space-between;
            z-index: 10;
        }
        
        .battlefield-info {
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 12px;
            line-height: 1.5;
        }
        
        .entity-counter {
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 15px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        .red-force { color: #ef4444; }
        .blue-force { color: #3b82f6; }
        .neutral-force { color: #ffffff; }
        
        /* 模拟实体 */
        .entity {
            position: absolute;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        
        .entity.red { background: #ef4444; top: 30%; left: 20%; }
        .entity.red:nth-child(2) { top: 40%; left: 25%; }
        .entity.red:nth-child(3) { top: 35%; left: 30%; }
        
        .entity.blue { background: #3b82f6; top: 60%; left: 70%; }
        .entity.blue:nth-child(2) { top: 65%; left: 75%; }
        .entity.blue:nth-child(3) { top: 55%; left: 80%; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.2); }
        }
        
        /* 浮动工具栏 */
        .floating-toolbar {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 25px;
            padding: 5px;
            display: flex;
            gap: 5px;
        }
        
        .tool-btn {
            width: 40px;
            height: 40px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.2s;
        }
        
        .tool-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        /* 右侧信息面板 */
        .info-panel {
            width: 320px;
            background: #374151;
            border-left: 1px solid #4b5563;
            overflow-y: auto;
        }
        
        .panel-section {
            margin: 15px;
            background: #4b5563;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .panel-header {
            background: #6b7280;
            padding: 12px 15px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .panel-content {
            padding: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .info-value {
            color: #10b981;
            font-weight: bold;
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #374151;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 5px;
        }
        
        .progress-fill {
            height: 100%;
            background: #10b981;
            transition: width 0.3s;
        }
        
        /* 底部状态栏 */
        .footer {
            height: 30px;
            background: #1e3a8a;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-size: 11px;
            border-top: 1px solid #3b82f6;
        }
        
        .status-item {
            margin-right: 30px;
            display: flex;
            align-items: center;
        }
        
        .status-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #10b981;
            margin-right: 5px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="header-left">
            <div class="logo">军</div>
            <div class="system-title">作战模拟仿真训练系统 V1.0</div>
        </div>
        <div class="header-right">
            <span>当前用户：张指挥员</span>
            <span>2025-01-15 14:30:25</span>
            <span><span class="status-indicator"></span>系统正常</span>
            <button style="background: #ef4444; border: none; color: white; padding: 5px 15px; border-radius: 3px; cursor: pointer;">退出登录</button>
        </div>
    </header>

    <!-- 主体容器 -->
    <div class="main-container">
        <!-- 左侧功能菜单 -->
        <nav class="sidebar">
            <div class="menu-group">
                <div class="menu-title">📋 作战筹划</div>
                <div class="menu-items">
                    <div class="menu-item">任务理解</div>
                    <div class="menu-item">情况分析</div>
                    <div class="menu-item">方案制定</div>
                    <div class="menu-item active">想定编辑</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">⚡ 推演控制</div>
                <div class="menu-items">
                    <div class="menu-item">推演启动</div>
                    <div class="menu-item">过程控制</div>
                    <div class="menu-item">状态监控</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">🎯 兵力指挥</div>
                <div class="menu-items">
                    <div class="menu-item">兵力编成</div>
                    <div class="menu-item">指挥控制</div>
                    <div class="menu-item">行动引导</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">📊 导调评估</div>
                <div class="menu-items">
                    <div class="menu-item">导调计划</div>
                    <div class="menu-item">效果裁决</div>
                    <div class="menu-item">评估分析</div>
                    <div class="menu-item">复盘回放</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">🎮 终端管理</div>
                <div class="menu-items">
                    <div class="menu-item">席位分配</div>
                    <div class="menu-item">终端状态</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">⚙️ 系统管理</div>
                <div class="menu-items">
                    <div class="menu-item">用户管理</div>
                    <div class="menu-item">权限设置</div>
                    <div class="menu-item">系统监控</div>
                </div>
            </div>
        </nav>

        <!-- 中央态势显示区 -->
        <main class="main-content">
            <div class="battlefield-view">
                <div class="battlefield-overlay">
                    <div class="battlefield-info">
                        <div>🗺️ 地理环境：台海地区</div>
                        <div>⏰ 当前时间：D+2 06:30</div>
                        <div>⚡ 仿真比例：1:5</div>
                        <div>👁️ 显示模式：三维视角</div>
                        <div>🌤️ 天气：多云 能见度8km</div>
                        <div>🌊 海况：3级海况</div>
                        <div>💨 风向：东南风 4级</div>
                    </div>
                    <div class="entity-counter">
                        <div class="red-force">🔴 红方：152个实体</div>
                        <div class="blue-force">🔵 蓝方：89个实体</div>
                        <div class="neutral-force">⚪ 中性：15个实体</div>
                    </div>
                </div>
                
                <!-- 模拟实体 -->
                <div class="entity red"></div>
                <div class="entity red"></div>
                <div class="entity red"></div>
                <div class="entity blue"></div>
                <div class="entity blue"></div>
                <div class="entity blue"></div>
                
                <!-- 浮动工具栏 -->
                <div class="floating-toolbar">
                    <button class="tool-btn" title="暂停">⏸️</button>
                    <button class="tool-btn" title="加速">⏩</button>
                    <button class="tool-btn" title="重置">🔄</button>
                    <button class="tool-btn" title="保存">💾</button>
                    <button class="tool-btn" title="截图">📷</button>
                    <button class="tool-btn" title="录制">🎥</button>
                </div>
            </div>
        </main>

        <!-- 右侧信息面板 -->
        <aside class="info-panel">
            <div class="panel-section">
                <div class="panel-header">📈 实时状态</div>
                <div class="panel-content">
                    <div class="info-item">
                        <span>系统负载</span>
                        <span class="info-value">68%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 68%"></div>
                    </div>
                    <div class="info-item">
                        <span>在线用户</span>
                        <span class="info-value">45人</span>
                    </div>
                    <div class="info-item">
                        <span>活跃席位</span>
                        <span class="info-value">32个</span>
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-header">🎯 当前任务</div>
                <div class="panel-content">
                    <div class="info-item">
                        <span>训练科目</span>
                        <span class="info-value">两栖突击</span>
                    </div>
                    <div class="info-item">
                        <span>训练阶段</span>
                        <span class="info-value">立体突击</span>
                    </div>
                    <div class="info-item">
                        <span>剩余时间</span>
                        <span class="info-value">45分钟</span>
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-header">📋 消息中心</div>
                <div class="panel-content">
                    <div class="info-item">
                        <span>指挥命令</span>
                        <span class="info-value">3条</span>
                    </div>
                    <div class="info-item">
                        <span>系统通知</span>
                        <span class="info-value">1条</span>
                    </div>
                    <div class="info-item">
                        <span>告警信息</span>
                        <span class="info-value">0条</span>
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-header">📊 快速统计</div>
                <div class="panel-content">
                    <div class="info-item">
                        <span>任务完成率</span>
                        <span class="info-value">75%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 75%"></div>
                    </div>
                    <div class="info-item">
                        <span>兵力损失率</span>
                        <span class="info-value">12%</span>
                    </div>
                    <div class="info-item">
                        <span>目标摧毁率</span>
                        <span class="info-value">68%</span>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <!-- 底部状态栏 -->
    <footer class="footer">
        <div class="status-item">
            <span class="status-dot"></span>
            <span>推演状态：运行中</span>
        </div>
        <div class="status-item">
            <span class="status-dot"></span>
            <span>网络状态：正常</span>
        </div>
        <div class="status-item">
            <span class="status-dot"></span>
            <span>数据库：连接正常</span>
        </div>
        <div class="status-item">
            <span class="status-dot"></span>
            <span>仿真引擎：运行正常</span>
        </div>
        <div class="status-item">
            <span>帧率：30fps</span>
        </div>
        <div class="status-item">
            <span>延迟：45ms</span>
        </div>
    </footer>

    <script>
        // 简单的交互效果
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 实时时间更新
        function updateTime() {
            const now = new Date();
            const timeStr = now.getFullYear() + '-' + 
                          String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0') + ':' +
                          String(now.getSeconds()).padStart(2, '0');
            document.querySelector('.header-right span:nth-child(2)').textContent = timeStr;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
    </script>
</body>
</html>
