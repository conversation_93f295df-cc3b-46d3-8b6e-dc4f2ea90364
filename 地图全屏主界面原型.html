<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>作战模拟仿真训练系统 - 地图全屏界面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #000000;
            color: #ffffff;
            height: 100vh;
            overflow: hidden;
        }
        
        /* 全屏地图容器 */
        .map-container {
            width: 100%;
            height: 100vh;
            background: linear-gradient(45deg, #001122 0%, #003366 30%, #004488 60%, #001122 100%);
            position: relative;
            overflow: hidden;
        }
        
        /* 地图网格效果 */
        .map-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            pointer-events: none;
        }
        
        /* 顶部浮动导航栏 */
        .floating-header {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            height: 60px;
            background: rgba(30, 58, 138, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1000;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }
        
        .logo {
            width: 40px;
            height: 40px;
            background: #ef4444;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .system-title {
            font-size: 18px;
            font-weight: bold;
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: 30px;
            font-size: 14px;
        }
        
        /* 左侧浮动功能面板 */
        .floating-menu {
            position: absolute;
            top: 100px;
            left: 20px;
            width: 280px;
            max-height: calc(100vh - 140px);
            background: rgba(55, 65, 81, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            border: 1px solid rgba(75, 85, 99, 0.5);
            z-index: 900;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }
        
        .floating-menu.collapsed {
            transform: translateX(-260px);
        }
        
        .menu-toggle {
            position: absolute;
            top: 50%;
            right: -40px;
            width: 40px;
            height: 80px;
            background: rgba(55, 65, 81, 0.95);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-left: none;
            border-radius: 0 10px 10px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #ffffff;
            transform: translateY(-50%);
        }
        
        .menu-group {
            margin: 10px;
        }
        
        .menu-title {
            padding: 15px 20px;
            background: rgba(75, 85, 99, 0.8);
            font-weight: bold;
            font-size: 14px;
            cursor: pointer;
            border-radius: 8px;
            margin-bottom: 5px;
            border-left: 4px solid #3b82f6;
        }
        
        .menu-items {
            background: rgba(55, 65, 81, 0.6);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .menu-item {
            padding: 12px 20px;
            cursor: pointer;
            font-size: 13px;
            border-bottom: 1px solid rgba(75, 85, 99, 0.3);
            transition: background 0.2s;
        }
        
        .menu-item:hover {
            background: rgba(75, 85, 99, 0.8);
        }
        
        .menu-item.active {
            background: rgba(59, 130, 246, 0.8);
            color: #ffffff;
        }
        
        /* 右侧浮动信息面板 */
        .floating-info {
            position: absolute;
            top: 100px;
            right: 20px;
            width: 320px;
            max-height: calc(100vh - 140px);
            background: rgba(55, 65, 81, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            border: 1px solid rgba(75, 85, 99, 0.5);
            z-index: 900;
            overflow-y: auto;
            transition: transform 0.3s ease;
        }
        
        .floating-info.collapsed {
            transform: translateX(300px);
        }
        
        .info-toggle {
            position: absolute;
            top: 50%;
            left: -40px;
            width: 40px;
            height: 80px;
            background: rgba(55, 65, 81, 0.95);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-right: none;
            border-radius: 10px 0 0 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #ffffff;
            transform: translateY(-50%);
        }
        
        .panel-section {
            margin: 15px;
            background: rgba(75, 85, 99, 0.8);
            border-radius: 8px;
            overflow: hidden;
        }
        
        .panel-header {
            background: rgba(107, 114, 128, 0.8);
            padding: 12px 15px;
            font-weight: bold;
            font-size: 14px;
        }
        
        .panel-content {
            padding: 15px;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .info-value {
            color: #10b981;
            font-weight: bold;
        }
        
        /* 底部浮动状态栏 */
        .floating-footer {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            height: 40px;
            background: rgba(30, 58, 138, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            font-size: 12px;
            z-index: 1000;
            border: 1px solid rgba(59, 130, 246, 0.3);
        }
        
        .status-item {
            margin-right: 30px;
            display: flex;
            align-items: center;
        }
        
        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #10b981;
            margin-right: 8px;
        }
        
        /* 中央浮动工具栏 */
        .central-toolbar {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border-radius: 25px;
            padding: 10px;
            display: flex;
            gap: 10px;
            z-index: 800;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .tool-btn {
            width: 50px;
            height: 50px;
            border: none;
            background: rgba(255, 255, 255, 0.1);
            color: #ffffff;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            transition: all 0.2s;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .tool-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: scale(1.1);
        }
        
        /* 地图上的实体 */
        .entity {
            position: absolute;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            animation: pulse 2s infinite;
            cursor: pointer;
            border: 2px solid rgba(255, 255, 255, 0.5);
        }
        
        .entity.red { 
            background: #ef4444; 
            top: 30%; 
            left: 20%;
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.6);
        }
        .entity.red:nth-child(2) { top: 40%; left: 25%; }
        .entity.red:nth-child(3) { top: 35%; left: 30%; }
        .entity.red:nth-child(4) { top: 45%; left: 22%; }
        .entity.red:nth-child(5) { top: 32%; left: 28%; }
        
        .entity.blue { 
            background: #3b82f6; 
            top: 60%; 
            left: 70%;
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.6);
        }
        .entity.blue:nth-child(2) { top: 65%; left: 75%; }
        .entity.blue:nth-child(3) { top: 55%; left: 80%; }
        .entity.blue:nth-child(4) { top: 70%; left: 72%; }
        .entity.blue:nth-child(5) { top: 58%; left: 78%; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.3); }
        }
        
        /* 地图信息覆盖层 */
        .map-overlay {
            position: absolute;
            top: 100px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 14px;
            z-index: 700;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .map-info-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            text-align: center;
        }
        
        .map-info-item {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .map-info-label {
            font-size: 12px;
            color: #9ca3af;
            margin-bottom: 5px;
        }
        
        .map-info-value {
            font-size: 16px;
            font-weight: bold;
            color: #ffffff;
        }
        
        /* 实体计数器 */
        .entity-counter {
            position: absolute;
            top: 100px;
            right: 360px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            padding: 15px;
            border-radius: 10px;
            font-size: 14px;
            z-index: 700;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .force-count {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }
        
        .force-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
        }
        
        .red-force { color: #ef4444; }
        .blue-force { color: #3b82f6; }
        .neutral-force { color: #ffffff; }
        
        /* 响应式设计 */
        @media (max-width: 1600px) {
            .floating-menu { width: 250px; }
            .floating-info { width: 280px; }
        }
        
        @media (max-width: 1200px) {
            .floating-menu.collapsed { transform: translateX(-230px); }
            .floating-info.collapsed { transform: translateX(260px); }
        }
    </style>
</head>
<body>
    <!-- 全屏地图容器 -->
    <div class="map-container">
        <!-- 顶部浮动导航栏 -->
        <header class="floating-header">
            <div class="header-left">
                <div class="logo">军</div>
                <div class="system-title">作战模拟仿真训练系统 V1.0</div>
            </div>
            <div class="header-right">
                <span>当前用户：张指挥员</span>
                <span id="current-time">2025-01-15 14:30:25</span>
                <span><span class="status-dot"></span>系统正常</span>
                <button style="background: #ef4444; border: none; color: white; padding: 8px 15px; border-radius: 5px; cursor: pointer;">退出登录</button>
            </div>
        </header>

        <!-- 地图信息覆盖层 -->
        <div class="map-overlay">
            <div class="map-info-grid">
                <div class="map-info-item">
                    <div class="map-info-label">地理环境</div>
                    <div class="map-info-value">台海地区</div>
                </div>
                <div class="map-info-item">
                    <div class="map-info-label">当前时间</div>
                    <div class="map-info-value">D+2 06:30</div>
                </div>
                <div class="map-info-item">
                    <div class="map-info-label">仿真比例</div>
                    <div class="map-info-value">1:5</div>
                </div>
                <div class="map-info-item">
                    <div class="map-info-label">天气状况</div>
                    <div class="map-info-value">多云 8km</div>
                </div>
                <div class="map-info-item">
                    <div class="map-info-label">海况</div>
                    <div class="map-info-value">3级海况</div>
                </div>
                <div class="map-info-item">
                    <div class="map-info-label">风向风力</div>
                    <div class="map-info-value">东南风4级</div>
                </div>
            </div>
        </div>

        <!-- 实体计数器 -->
        <div class="entity-counter">
            <div class="force-count red-force">
                <div class="force-indicator" style="background: #ef4444;"></div>
                <span>红方：152个实体</span>
            </div>
            <div class="force-count blue-force">
                <div class="force-indicator" style="background: #3b82f6;"></div>
                <span>蓝方：89个实体</span>
            </div>
            <div class="force-count neutral-force">
                <div class="force-indicator" style="background: #ffffff;"></div>
                <span>中性：15个实体</span>
            </div>
        </div>

        <!-- 左侧浮动功能面板 -->
        <nav class="floating-menu" id="floatingMenu">
            <div class="menu-toggle" onclick="toggleMenu()">◀</div>
            
            <div class="menu-group">
                <div class="menu-title">📋 作战筹划</div>
                <div class="menu-items">
                    <div class="menu-item">任务理解</div>
                    <div class="menu-item">情况分析</div>
                    <div class="menu-item">方案制定</div>
                    <div class="menu-item active">想定编辑</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">⚡ 推演控制</div>
                <div class="menu-items">
                    <div class="menu-item">推演启动</div>
                    <div class="menu-item">过程控制</div>
                    <div class="menu-item">状态监控</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">🎯 兵力指挥</div>
                <div class="menu-items">
                    <div class="menu-item">兵力编成</div>
                    <div class="menu-item">指挥控制</div>
                    <div class="menu-item">行动引导</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">📊 导调评估</div>
                <div class="menu-items">
                    <div class="menu-item">导调计划</div>
                    <div class="menu-item">效果裁决</div>
                    <div class="menu-item">评估分析</div>
                    <div class="menu-item">复盘回放</div>
                </div>
            </div>
            
            <div class="menu-group">
                <div class="menu-title">🎮 终端管理</div>
                <div class="menu-items">
                    <div class="menu-item">席位分配</div>
                    <div class="menu-item">终端状态</div>
                </div>
            </div>
        </nav>

        <!-- 右侧浮动信息面板 -->
        <aside class="floating-info" id="floatingInfo">
            <div class="info-toggle" onclick="toggleInfo()">▶</div>
            
            <div class="panel-section">
                <div class="panel-header">📈 实时状态</div>
                <div class="panel-content">
                    <div class="info-item">
                        <span>系统负载</span>
                        <span class="info-value">68%</span>
                    </div>
                    <div class="info-item">
                        <span>在线用户</span>
                        <span class="info-value">45人</span>
                    </div>
                    <div class="info-item">
                        <span>活跃席位</span>
                        <span class="info-value">32个</span>
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-header">🎯 当前任务</div>
                <div class="panel-content">
                    <div class="info-item">
                        <span>训练科目</span>
                        <span class="info-value">两栖突击</span>
                    </div>
                    <div class="info-item">
                        <span>训练阶段</span>
                        <span class="info-value">立体突击</span>
                    </div>
                    <div class="info-item">
                        <span>剩余时间</span>
                        <span class="info-value">45分钟</span>
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-header">📋 消息中心</div>
                <div class="panel-content">
                    <div class="info-item">
                        <span>指挥命令</span>
                        <span class="info-value">3条</span>
                    </div>
                    <div class="info-item">
                        <span>系统通知</span>
                        <span class="info-value">1条</span>
                    </div>
                    <div class="info-item">
                        <span>告警信息</span>
                        <span class="info-value">0条</span>
                    </div>
                </div>
            </div>
            
            <div class="panel-section">
                <div class="panel-header">📊 快速统计</div>
                <div class="panel-content">
                    <div class="info-item">
                        <span>任务完成率</span>
                        <span class="info-value">75%</span>
                    </div>
                    <div class="info-item">
                        <span>兵力损失率</span>
                        <span class="info-value">12%</span>
                    </div>
                    <div class="info-item">
                        <span>目标摧毁率</span>
                        <span class="info-value">68%</span>
                    </div>
                </div>
            </div>
        </aside>

        <!-- 中央浮动工具栏 -->
        <div class="central-toolbar">
            <button class="tool-btn" title="暂停推演">⏸️</button>
            <button class="tool-btn" title="加速推演">⏩</button>
            <button class="tool-btn" title="重置推演">🔄</button>
            <button class="tool-btn" title="保存状态">💾</button>
            <button class="tool-btn" title="截图">📷</button>
            <button class="tool-btn" title="录制">🎥</button>
        </div>

        <!-- 地图上的实体 -->
        <div class="entity red" title="红方舰艇编队"></div>
        <div class="entity red" title="红方登陆艇"></div>
        <div class="entity red" title="红方直升机"></div>
        <div class="entity red" title="红方装甲车"></div>
        <div class="entity red" title="红方火炮阵地"></div>
        
        <div class="entity blue" title="蓝方防御阵地"></div>
        <div class="entity blue" title="蓝方巡逻艇"></div>
        <div class="entity blue" title="蓝方雷达站"></div>
        <div class="entity blue" title="蓝方导弹阵地"></div>
        <div class="entity blue" title="蓝方指挥所"></div>

        <!-- 底部浮动状态栏 -->
        <footer class="floating-footer">
            <div class="status-item">
                <span class="status-dot"></span>
                <span>推演状态：运行中</span>
            </div>
            <div class="status-item">
                <span class="status-dot"></span>
                <span>网络：正常</span>
            </div>
            <div class="status-item">
                <span class="status-dot"></span>
                <span>数据库：连接正常</span>
            </div>
            <div class="status-item">
                <span class="status-dot"></span>
                <span>仿真引擎：正常</span>
            </div>
            <div class="status-item">
                <span>帧率：30fps</span>
            </div>
            <div class="status-item">
                <span>延迟：45ms</span>
            </div>
        </footer>
    </div>

    <script>
        // 菜单折叠功能
        function toggleMenu() {
            const menu = document.getElementById('floatingMenu');
            const toggle = menu.querySelector('.menu-toggle');
            menu.classList.toggle('collapsed');
            toggle.textContent = menu.classList.contains('collapsed') ? '▶' : '◀';
        }
        
        function toggleInfo() {
            const info = document.getElementById('floatingInfo');
            const toggle = info.querySelector('.info-toggle');
            info.classList.toggle('collapsed');
            toggle.textContent = info.classList.contains('collapsed') ? '◀' : '▶';
        }
        
        // 菜单项点击
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelectorAll('.menu-item').forEach(i => i.classList.remove('active'));
                this.classList.add('active');
            });
        });
        
        // 实时时间更新
        function updateTime() {
            const now = new Date();
            const timeStr = now.getFullYear() + '-' + 
                          String(now.getMonth() + 1).padStart(2, '0') + '-' + 
                          String(now.getDate()).padStart(2, '0') + ' ' +
                          String(now.getHours()).padStart(2, '0') + ':' +
                          String(now.getMinutes()).padStart(2, '0') + ':' +
                          String(now.getSeconds()).padStart(2, '0');
            document.getElementById('current-time').textContent = timeStr;
        }
        
        setInterval(updateTime, 1000);
        updateTime();
        
        // 实体点击事件
        document.querySelectorAll('.entity').forEach(entity => {
            entity.addEventListener('click', function() {
                const title = this.getAttribute('title');
                alert(`选中实体：${title}`);
            });
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'F11') {
                e.preventDefault();
                if (document.fullscreenElement) {
                    document.exitFullscreen();
                } else {
                    document.documentElement.requestFullscreen();
                }
            } else if (e.key === 'Escape') {
                // 取消选择等操作
            } else if (e.key === ' ') {
                e.preventDefault();
                // 暂停/继续推演
                console.log('暂停/继续推演');
            }
        });
    </script>
</body>
</html>
