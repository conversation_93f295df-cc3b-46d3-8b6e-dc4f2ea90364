# 功能组成

## 1.作战筹划与想定编辑子系统（J）

### 理解作战任务

- 14.能够导入作战任务，支持任务的新建、修改、删除等管理操作；能够辅助用户进行作战任务分解，针对两栖作战任务需求，支持生成集结装载、海上航渡、展开换乘、扫残破障、火力支援、立体突击、综合防御等子任务清单，支持子任务清单的增、删、改、查等管理操作；能够以思维导图、任务网络图等多种形式呈现任务间关系，辅助分析两栖作战任务。

### 分析判断情况

- 15.能够为两栖作战敌情分析、战场环境分析、我情分析提供分析工具和信息支撑；能够可视化展现登陆场海域及水际滩头的障碍、工事、火力点等敌方兵力部署信息；能够可视化展现我方装备类型、数量、部署情况、装备性能等我方兵力部署信息；能够可视化展现登陆场附近潮汐、海流、风场、气象、滩头底质等水文气象环境信息；能够进行敌我作战能力和对抗关系的可视化态势分析，并形成情况判断结论。

### 制定作战方案

- 16.能够根据上级意图及分析判断结论制定作战方案，确定作战目的、作战区域、作战方向、重点目标、作战方式、作战阶段、兵力部署、协同方式、完成时限等内容；能够根据作战方案生成对应作战文书；支持作战计划文件的导入导出。

### 制定作战计划

- 17.能够根据选定作战方案制定两栖作战总体计划和分支计划，并生成协同计划表；能够根据行动计划生成对应作战文书；能够根据行动计划生成格式化文件，支持作战计划文件的导入导出。作战计划包括但不限于两栖作战总体计划，分支计划包括但不限于机动集结计划、兵力装载卸载计划、海上航渡计划、综合防御计划、扫残破障计划、火力支援计划、立体突击计划等。

### 预制模板与方案（计划）

- 18.预置≮15种作战方案、作战计划模板，包含但不限于装卸载、综合防御、火力支援、扫残破障、突击上陆等。

- 19.作战筹划：支持生成通用格式文档的作战方案、计划文书，包含但不限于WPS、pdf格式。

- 20.作战筹划：提供图形化和文本等方式的作战方案、计划录入功能。

- 21.作战筹划：方案（计划）数据存储：支持文件型和网络型关系数据库。

### 作战筹划工具

- 22.作战筹划：筹划工具≮15个，包含但不限于综合查询、地图量算分析、机动绘算、潮汐计算、装载能力计算、战斗能力计算、风流计算、地形分析、登陆点分析、机降场分析、航线规划、测量分析、底质分析等。

### 想定编辑管理

- 83.想定编辑功能：能够支持包括但不限于提供专业化的想定编辑工具，能够设置作战时间、作战环境、兵力编成、装备和设施部署等想定要素；可加载地理信息数据、军事标绘数据，支持基于虚拟战场环境的各作战要素编辑、设置和部署；能够进行兵力行动规划、指挥关系设置、通信组网设置、实力对比分析、攻防策略设置等。

- 84.想定编辑数据输出包括本地数据库、文本文档等多种格式。

##  2.作战兵力模拟子系统

### 兵力模型管理

- 44	作战兵力-两栖作战兵力：能够模拟两栖作战兵力≮9类34型，包括但不限于两栖舰艇、舰载XXX、装甲装备、火炮装备、保障装备、指控装备、防空装备、反装甲装备、支援掩护兵力等，提供三维模型以及组件化建模。XXXXXXXXXXX

### 兵力桌面模拟终端

- 45	两栖作战兵力桌面模拟终端：功能级三维建模，能够通过键盘、鼠标、操纵杆、显示屏等为训练人员构建战术级训练模拟终端≮10型，包含但不限于XXXXXXX。各模拟终端满足XXXXXX的XXX标准协议适配，能够与仿真平台互联互通。

- ★	46	XXXX两栖攻击舰：具有模拟海上航行与操纵功能，能够模拟车舵实现启动、转向、停泊、加（减）速功能，具有模拟发动机转速、舵角、航向、航速等主要仪表指示功能，能够反映舰艇的姿态受海浪、海流起伏的影响，能够模拟舱门开启关闭等功能，能够模拟装备在各装载舱室布列，能够实现气垫艇、XX车辆、舰载直升机装载（着舰）、卸载（离舰）模拟。

- ▲	47	XXXX气垫艇：具有模拟航行功能，能实现登陆艇启动、转向、停泊、加（减）速、挂挡（换挡）功能；具有模拟发动机转速、舵角、航向、航速等主要仪表指示功能；能够以三维视景仿真车舵手观察的景况；能够反映登陆艇的姿态受海浪起伏的影响；具有模拟海上航行与陆上机动操控功能，能实现模拟车钟控制、舵控制、舱门开启关闭等功能；具有模拟车辆、人员的装/卸载功能。

- ▲	48	XXX气垫艇：具有模拟航行功能，能实现登陆艇启动、转向、停泊、加（减）速、挂挡（换挡）功能；具有模拟发动机转速、舵角、航向、航速等主要仪表指示功能；能够以三维视景仿真车舵手观察的景况；能够反映登陆艇的姿态受海浪起伏的影响；具有模拟海上航行与陆上机动操控功能，能实现模拟车钟控制、舵控制、舱门开启关闭等功能；具有模拟车辆、人员的装/卸载功能；模拟进出坞功能；模拟登陆艇的运载功能；具有与母舰、搭载车辆、人员指挥引导和协同功能等。

- ▲	49	XXXX运输直升机、XXX突击直升机：具有模拟飞行功能，包括离舰、离地起飞飞行／吊装飞行／海（空）情处置飞行／空地（地空）引导/夜间空中突防飞行/夜间综合自卫飞行/夜间空（海）情处置飞行；具有模拟发动机转速、航向、高度、航速等主要仪表指示功能；具有模拟运输投送功能；具有模拟火力突击功能；具有模拟侦察功能；模拟舰上转运、离舰起飞、着舰等功能；模拟与母舰指挥引导功能等；具有模拟定位功能；具有与其他兵力指挥协同功能。

- ▲	50	XXXXX两栖装甲突击车：具有模拟驾驶功能，能实现车辆发动、加（减）速、挂档（换挡）、转向、刹车功能；具有模拟发动机转速、车速、油温、油量、里程等主要仪表指示的功能；能够以三维视景仿真驾驶员观察的景况；能够反映车辆的速度、姿态受地形起伏、障碍物的影响；具有模拟车辆定位导航功能；具有炮长选择弹种的功能；具有炮长观察、瞄准、测距和射击功能；具有车长观察和目标指示的功能；具有诸元解算功能；具有车长接收上级指挥命令、上报战果战损的功能；具有虚拟定位并上报本车位置的功能；具有模拟车内、车际通话的功能；具有模拟车际数据通信的功能；具有与其他兵力指挥协同功能。

- ▲	51	XXXXX两栖装甲步兵战车：具有模拟驾驶功能，能实现车辆发动、加（减）速、挂档（换挡）、转向、刹车功能；具有模拟发动机转速、车速、油温、油量、里程等主要仪表指示的功能；能够以三维视景仿真驾驶员观察的景况；能够反映车辆的速度、姿态受地形起伏、障碍物的影响；具有模拟车辆定位导航功能；具有炮长选择弹种的功能；具有炮长观察、瞄准、测距和射击功能；具有车长观察和目标指示的功能；具有诸元解算功能；具有车长接收上级指挥命令、上报战果战损的功能；具有虚拟定位并上报本车位置的功能；具有模拟车内、车际通话的功能；具有模拟车际数据通信的功能；具有与其他兵力指挥协同功能。

- ▲	52	XXXXX自行榴弹炮：具有模拟驾驶功能，能实现车辆发动、加（减）速、挂档（换挡）、转向、刹车功能；具有模拟发动机转速、车速、油温、油量、里程等主要仪表指示的功能；能够以三维视景仿真驾驶员观察的景况；能够反映车辆的速度、姿态受地形起伏、障碍物的影响；具有模拟车辆定位导航功能；具有炮长观察战场环境的功能；具有瞄准手显示器接收和显示诸元功能；具有装填手显示器接收和显示装填诸元的功能；具有瞄准手进行方向瞄准和射角装定的功能；具有诸元解算功能；具有瞄准手进行直瞄、间瞄射击的功能；具有模拟车际通话的功能；具有模拟车内通话的功能；具有与其他兵力指挥协同功能。

### 蓝方作战模拟

- 54	作战兵力-蓝军兵力：能够模拟X军、X军、X军、X自卫队主战舰艇、XXX、潜艇、无人作战兵力等；能够模拟XXX抗登陆兵力，以及各型抗登陆障碍、设施等。

- ★	103	蓝方作战模拟：兵力种类≮8类，≮50种，包括X军和X军水面舰艇、潜艇、XXX、岸导、火箭炮、火炮、防空导弹、人员等，X军和X军水面舰艇、潜艇、XXX等。

- 104	蓝方作战模拟：雷障≮5类，包括轨条砦、三角锥、铁丝网、地雷、渔网等。

- 105	蓝方作战模拟：兵力行动类型≮8种，兵力编组、攻击、防御、侦察、集结、解散、机动、布设工事障碍等。

- 106	蓝方作战模拟：提供对作战兵力实体的增加、修改、删除功能。

### 协同作战与联合行动

- 53	作战兵力-协同兵力：能够模拟两栖作战协同兵力，包括但不限于XX主战航母、驱逐舰、护卫舰、XXXXX、潜艇等兵力。可以规划不同兵种、部队兵力部署位置和行动计划，实现兵力的有效协同作战。

## 3.仿真引擎与推演控制子系统

### 推演方案加载与编辑

- 23.仿真推演-推演方案编辑：能够加载作战筹划生成的作战方案和计划，并实施修改；能够手动录入作战方案和计划，并生成初始推演方案，形成初始推演态势。

### 时间推进与倍率控制

- 26.仿真推演加速比：仿真实体数量≤2000个时，仿真加速比≥20，可调整运行加速比。

- 6.仿真时比：1:1～1：20，支持可编辑步长仿真运行。

### 仿真过程控制

- 24.仿真推演-仿真推演控制：能够实现对仿真推演的过程控制，可以调整仿真推演运行倍速、实施推演启动、暂停、继续、后退、停止等操作；具备断点重续功能；具备推演方式设置、推演速率控制、推演节点记录、推演节点检查、推演节点加载、推演状态控制等功能。

- 25.仿真推演方式设置：包括人在环和人不在环两种模式；能够通过时间轴、作战阶段、作战空间、作战事件等进行仿真推演编辑和推演控制。

### 人在回路实时干预

- 27.仿真推演过程中的实时干预：提供兵力机动、打击、毁伤、复活、评估等方面的人在回路干预能力。

### 战场环境模拟

### 交战与效果裁决

- ▲	32	裁决评估：支持对舰打击、对空打击、对地打击、对水下打击、电子对抗、人员杀伤等效果的系统裁决和人工裁决。

### 多用户协同与并行仿真

- 34	推演应用模式：支持多业务并行和多用户协同的应用模式，支持同时仿真访问用户数量数不小于80。

### 推演结果监控与分析

- ▲	28	仿真推演-推演结果分析：能够实现战损监控、消耗监控和方案优选。

- ▲	29	战损监控：采用统计图表的形式，可视化展现各方战损情况数据，可按武器装备类型进行统计展现、按编成结构进行统计展现、按人员类型进行统计展现等，各方展现使用的样式、颜色可通过选择设置区分。

- 30	消耗监控：能够对各方的弹药、油料消耗信息进行实时监控和统计展现，能够生成弹药/油料消耗实时统计表、实时对比图；能实时统计并显示选中编成单位的弹药、油料消耗信息。

- 31	方案优选：针对作战方案是否符合作战整体意图和作战任务进行横向比对，对经过推演后的作战结果进行量化对比，是否完成作战部队任务及任务完成度进行对比，对任务完成后的战损和消耗情况进行对比，计算作战方案的作战效益等因素，多方因素综合判定，推选出最优作战方案。

### 复盘与回放

- ▲	33	复盘文件要求：支持大于2GB文件回放。

## 4.两栖作战行动仿真子系统（F）

### 装卸载作业行动

- 55	作战行动模拟-装卸载类作战行动≮6种，包括但不限于码头装载、浮渡装载、抵滩装载、垂直装载、换乘卸载（抵滩卸载）。

### 近距火力支援行动

- 57	作战行动模拟-近距火力支援作战行动≮3种，包括但不限于空中近距火力支援、舰炮火力支援、XX火炮火力支援等。

### 扫残破障行动

- 56	作战行动模拟-扫残破障类作战行动≮4种，包括但不限于雷障侦察、清扫雷障、清除障碍物、标示通路等。

### 立体突击行动

- 58	作战行动模拟-立体突击类作战行动≮3种，包括但不限于垂直突击、掠海突击、平面突击。

### 综合防御行动

- 59	作战行动模拟-综合防御类作战行动≮4种，包括但不限于对空防御、对水下目标防御、对海上目标防御、对陆上目标防御。

## 5.指挥控制与引导子系统(F)

### 指挥关系建模

- 61.指挥控制：支持指挥流程和指挥关系的可视化构建与编辑。

- 62.指挥控制：指挥兵力规模≮300个。

### 兵力编成与部署

- 63.兵力编成：根据作战方案和作战要求对作战兵力进行编成，能够对编成兵力进行编辑、修改和保存等。

- 64.兵力部署：根据作战方案和作战计划对所属兵力进行部署，可以在电子地图上进行部署、也可以进行坐标录入部署，并对部署兵力进行可视化管理、显示部署态势。

### 指挥命令

- 60.指挥控制：指挥命令发送与接收反应时间≮1秒。

- 65.兵力指挥：能够对所指挥兵力通过指挥命令实施指挥，被指挥兵力能够正确接收和识别指挥命令，并响应指挥行动。

- 66.兵力控制：能够对所控制兵力通过控制指令实施控制，被控制兵力能够正确接收和识别控制指令，并响应控制行动。

### 指挥引导

- 67.指挥引导:能够对直升机、气垫艇和XX车辆等兵力进行指挥引导模拟；满足指挥引导直升机≮30架；气垫艇≮14艘；XX车辆≮80辆。

- 68.指挥引导-直升机：根据战场态势、运输直升机飞行计划及位置、状态等信息，监视运输直升机飞行，辅助对运输直升机编波、垂直登陆、返航及进近实施指挥引导；根据战场态势、突击直升机伴随护航、XX火力支援计划，及位置状态等信息，监视突击直升机飞行和任务执行情况，辅助对突击直升机伴随护航、XX火力支援、返航及进近实施指挥引导；进行直升机的空域使用冲突、航线飞行冲突、与防御火力冲突的实时判断，提供空域冲突告警提示和消解建议，支持直升机作战计划的实时调整；监视运输直升机装载、换乘和起降等行动。

- 69.指挥引导-气垫艇：根据战场态势、气垫艇航行计划及位置状态等信息，监视气垫艇的航行，辅助对气垫艇登陆和返航实施指挥引导；进行气垫艇的海域使用冲突、航路冲突、与防御火力冲突的实时判断，支持气垫艇航行计划的实时调整；监视气垫艇换乘、出坞和进坞等行动。

- 70.指挥引导-XX车辆：根据战场态势、XX车辆突击上陆计划及位置状态等信息，监视XX车辆突击上陆，辅助对XX车辆突击上陆、返航实施指挥引导；进行XX车辆的海域使用冲突、航路冲突、与防御火力冲突的实时判断，提供冲突告警提示和消解建议，支持XX车辆突击XX计划的实时调整；监视XX车辆泛水出坞、进坞等行动。

## 6.训练管理子系统

### 用户与权限管理

- 94.训练管理：构建统一的登录体系，包括用户信息管理、身份验证、会话管理等模块。在访问系统平台时，可以通过网关进行拦截校验，实现统一登录功能。在统一登录界面进行一次身份验证，即可无缝访问所有已授权的应用或系统。

- 97.训练管理：可管理的用户数量≥500个。

- 98.训练管理：单用户认证及登录处理响应时间≤3秒。

### 训练计划与方案管理

- 96.训练管理：支持训练计划、方案文档的导入导出。

### 席位分配与管理

- 95.训练管理：能够对训练席位进行动态分配，并进行实时状态监控。

- 99.训练管理：支持多用户并行访问，最小并发访问数量≥200个。

- ★	11	席位构设规模支持≮80个席位，并可进行虚拟席位设置。

### 系统运行监测

- 100.训练管理：运行监测日志记录数量≥100000条。

- 101.训练管理：支持在用户界面上显示≮100个被管设备及故障状态。

- 102.训练管理：支持CPU使用率、CPU温度、内存使用率、占用内存大小、磁盘使用率等≮5种量化指标的阀值预警，支持预警阀值设置的生效时间不大于5秒。

## 7.导调监控与评估子系统

### 总体描述

- 85.能够依据训练导调计划产生的导调数据，引导整个演练按计划进行；在演练过程中，可根据演练情节，实现对训练环境下的虚拟兵力实体、状态的控制，对仿真战场环境的调整与控制，对虚拟兵力的随机指挥和干预等功能。

### 导调计划

- 86.导调内容：不少于地理环境、气象环境、水文环境、战场态势、战场目标、兵力行动、兵力控制等方面。

- 87.导调方式：支持计划导调、临机导调等2种方式，每种方式均支持导调计划的拟制、管理和发布。

### 导调评估算法

- 88.导调评估算法：支持≮3种评估算法，并支持可编辑扩展。

### 导播监控

### 导调裁决

- 89.导调裁决：根据红蓝双方兵力对抗行动和毁伤效能评估规则进行系统裁决，也可以人工干预裁决，并记录和反馈裁决结果。

### 评估分析

- 91.评估方式：支持事后评估、在线评估和综合评估，其中，事后评估支持对多组仿真训练数据同时计算和对比分析。

- 92.评估报告生成：根据评估报告模板和用户定制信息，自动生成评估报告，并支持文档输出打印，包含但不限于WPS、pdf格式。

- 93.评估结果展现：支持表格、雷达图、直方图、饼图、散点图等结果可视化方式，支持用户自主设计评估结果的多维可视化。

### 复盘管理

- 90.训练复盘：可按作战任务流程、关键环节、关键事件、时间轴为主索引，对推演、训练全过程进行复盘；支持实现推演过程中的武器装备位置坐标、运动轨迹、战果战损情况等数据可视化展现。

## 8.三维可视化与人机交互子系统

### 战场三维场景渲染

- 10.单窗口三维场景中，显示1000个基本图元时，可以流畅操作和显示，平均帧速不低于30帧/秒。

- ▲	71	三维可视化：场景刷新帧率≮30fps。

- 72.三维可视化：三维模型的外形、纹理与实际装备、设施基本保持一致，具备各类装备的毁伤模型，保证模型的立体效果、光滑度和场景的运行速度，具备3级及以上LOD。

- 73.三维可视化：支持≮300个实体及三维军标的加载及显示。

### 战场可视化态势显示

- 9.二三维态势切换显示时间≤3秒，三维地理环境渲染展示耗时≤2秒。

- 35	态势感知：常规任务支持的目标数量不少于200个，重点目标识别监视任务支持的目标数量不少于50个。

- 74.三维可视化：支持实时态势显示和事后态势回放。

- 75.三维可视化：能够根据自然规律模拟光照、风速风向、动态云层等动态气象现象，也能由外部数据实时驱动显示。

- 76.三维可视化：能够根据参数设置，实时模拟雷、电、雨、雪等天气特效，并支持随时累积，地表产生积水、积雪效果。

- 77.三维可视化：能够模拟浪、流、潮汐数据，支持潮方向设置。

- 78.三维可视化：支持模拟海水颜色由深水区到浅水区的颜色过渡变化。

- 79.三维可视化：支持水下效果显示，包括水下能见度和颜色设置。

- 80.三维可视化：支持夜晚光照效果，包括建筑灯光和机场跑道灯光效果显示。

- 81.三维可视化：支持船舶尾流、爆炸烟火、飞行航迹等特效显示。物理引擎调用接口，支持调用碰撞检测、运动计算、实体毁伤等。

### 交互操作与视角控制

### 地图与地形处理工具

- 8.提供地图处理和转换工具,地图格式≥3种，支持读取的地理信息数据≥5种，包括卫星遥感影像、数字高程模型、矢量地图、全景影像、三维模型等。

- 82.三维可视化：能够快动自动生成全球植被数据，植被类型不小于100种，实时生成速度不大于2秒。

## 9.模拟终端接入与互联子系统

### 模拟终端类型支持

- 45.两栖作战兵力桌面模拟终端：功能级三维建模，能够通过键盘、鼠标、操纵杆、显示屏等为训练人员构建战术级训练模拟终端≮10型，包含但不限于XXXXXXX。各模拟终端满足XXXXXX的XXX标准协议适配，能够与仿真平台互联互通。

### 接口标准与协议

- 45.两栖作战兵力桌面模拟终端：功能级三维建模，能够通过键盘、鼠标、操纵杆、显示屏等为训练人员构建战术级训练模拟终端≮10型，包含但不限于XXXXXXX。各模拟终端满足XXXXXX的XXX标准协议适配，能够与仿真平台互联互通。

### 时间同步与坐标转换

### 互联互通与互操作

## 10.模型与数据资源中心子系统

### 基础数据管理

- 107.数据资源基础数据包括编制数据、人员数据、物资数据、目标特性数据和战场环境数据等。

### 实体模型管理

- 108.数据资源实体模型包括现役舰艇、XXX、岸舰导弹、XXX等主战武器装备和X军、X军、XX军部分武器装备，以及X军、X军、X军、XXXX队XX武器装备，具备三维实体、三维军标、二维军标的态势生成与显示功能。

- 2.（部分）同时仿真兵力数量≮3000个，系统支持兵力组件化建模，可仿真兵力种类：实体类≮12类，≮200种，包括但不限于陆战分队、人员、水面舰艇、飞机、潜艇、导弹作战单元、武器弹药、车辆火炮、障碍、设施、卫星、无人装备等实体；
装备实体包括但不限于驱护舰、两栖攻击舰、综合登陆舰、气垫艇、舰载直升机、固定翼飞机、装甲车、无人机。

### 组件模型管理

- 2.（部分）组件类不少于7类，≮200种，包括但不限于雷达、声纳、光学传感器、通信、数据链、发射具、战斗部组件模型。

### 行动模型管理

- 109.数据资源行动模型包括模拟兵力的指挥控制、战场机动、侦察监视、火力打击、战场防御、后装保障等作战行动。

### 任务模型管理

- 110.数据资源任务模型包括两栖作战装卸载任务、近距火力支援任务、扫残破障任务、立体突击、综合防御等任务及相应的分解任务。

### 分析评估模型管理

- 111.数据资源分析评估模型包括情报分析、能力计算、作战筹划、效能评估、方案评估等。

### 规则资源管理

- 112.数据资源规则资源包括机动规则、打击规则、侦察规则、情报规则、指挥规则、保障规则等。

### 战场环境资源中心

- 37.战场环境-地理基础数据要求：
（1）3D地图：具备全球3D地图显示功能。
（2）高程要求：中国高程精度不低于30m；东南沿海地区精度不低于5m。
（3）卫星地图：东南沿海地区分辨率不小于5米；提供卫星地图下载工具，保证需要时，中国全境（包括T岛）内重点关注地域，可下载分辨率不小于0.6米卫星地图数据。
（4）提供全国（包括T岛）公路导航数据，数据包含：高速、国道、省道、县道、乡道、城市内部主干道路等。
（5）提供全国（包括T岛）铁路数据，支持全国范围的铁路路径规划；提供全国铁路站点数据。
（6）提供全国（包括T岛）桥梁、隧道数据。

- 38.战场环境-地理重点数据要求：
（1）T岛全岛高程精度不低于5m，卫星地图分辨率不小于0.6m。
（2）提供台北、台中、桃园、台东、花莲、宜兰、基隆等T岛重点地区倾斜摄影数据和T岛所有城市建筑物数据。
（3）构建T岛重点登陆地段三维场景，包括：地形方面，DEM精度不低于1.5m，要能精确反映西海岸高低潮位线之间的潮间带缓坡、岸边陡坡、坝顶区域以及东海岸陡坡、悬崖、山腰公路沿线地形等微地貌；植被方面，构建沿岸地带海岸带植物群落模型，包括乔木类防风林、灌木、草，植物类型近似当地实景植物，植物的高度、密度与实景一致；大范围植被渲染流畅、不卡顿；水系方面，岸线陆地纵深5公里内，入海河流沟渠1m级地形建模、池塘湖泊1m级地形建模，河流湖泊水面真实感建模；交通方面，干线公路建模，模型要能反映桥墩、路基墙体、涵洞、栏杆等道路附属设施，路宽、路高、附属设施尺寸与实际一致；人工设施方面，构建码头、防波堤、岸边阶梯、禁渔设施、风力发电设施等。
（4）南海主要岛礁（包括我控岛礁、以及越、菲、马、台等占控岛礁）倾斜摄影模型建筑物数据。

## 11.战场环境构建子系统

### 战场环境构建

- 36.战场环境：能够模拟真实作战条件下的两栖战场环境，具体包括地理、气象、水文、电磁等≮4类，支持夜战环境模拟。

- 39.战场环境-地理环境：模拟对两栖作战有重要影响的地形、地貌、障碍、设施等环境要素。地形、地貌等环境数据能够模拟表现地形等高线和等深线、河流湖泊等陆地环境特征，能够模拟岸滩、港口、机（空）降场等要素特征，通过具体属性来反映陆地环境对作战行动的影响。支持防波堤、阻绝墙等坚固障碍，空降障碍物，轨条砦、三角锥和铁丝网等各类障碍物模拟；支持多种地形条件对车辆机动的影响建模。

- 40.战场环境-气象环境：气象数据能够模拟不同地区、季节和日夜交替下的大气环境的规律，并能以特定属性表现大气环境对人员、平台和系统的影响。支持全天候时间、昼夜、光照、阴晴、云、雨、雪、霜、雾、风、雷电等气象环境模型构建；支持多种气象环境对人员和两栖装备的影响建模；气象模块提供气象数据收集，包括温度、湿度、风速、风向、气压、降水量等基本气象参数的实时监测和历史数据记录。

- 41.战场环境-水文环境：水文数据能够模拟海浪、海流、潮汐随季节和日夜变化的规律，通过具体的模型模拟水文规律，能够仿真海洋环境要素对两栖作战的影响。

- 42.战场环境-三维场景建模：能够支持DEM数据及影像数据展现，提供≮6个三维场景，包括X岛区域≮4个（包括但不限于台东、花莲、宜兰、基隆）、南海区域≮1个（包括但不限于越占大中型岛礁），大陆海岸集结装载区域≮1个，每个场景陆地幅员≮10km×10km，海区幅员≮50km×50km。

- 43.战场环境效应：能够模拟生成的战场环境能够与平台机动、XX等模型实时交互，体现环境效应效果。

### 装备模型构建

### 场景编排

## 未在系统中的指标

### ★	1	仿真平台自主可控，系统主体功能支持运行于国产操作系统。

### ▲	3	训练科目不少于5类，≮15个，包括但不限于装卸载、扫残破障、近距火力支援、立体突击、综合防御训练。

### ▲	4	仿真推演精度，单兵，单平台。指数聚合模型到群体，平台实体模型到单平台单体，单元聚合模型到单个单元、设施模型到单个设施，支持平台装备、单元聚合、指数聚合三种类型仿真模型的混合交互仿真。

### 5	系统准备时间≤15分钟，复盘准备时间≤10分钟，生成评估报告时间≤5分钟。

### 7	态势刷新率≤1秒。

### ▲	12	仿真模式要求：系统支持实时和超实时两种仿真运行模式、人在环和人不在环两种使用模式。

### ▲	13	典型场景模拟能够满足装卸载、扫残破障、突击上陆、近距火力支援、综合防御等任务要求的典型作战场景≮6个，包含但不限于机场、港口、岛礁、登陆场等典型三维作战场景。

